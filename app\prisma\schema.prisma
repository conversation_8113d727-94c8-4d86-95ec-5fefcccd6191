generator client {
    provider = "prisma-client-js"
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

// Enums for better type safety
enum VehicleType {
  SEDAN
  SUV
  HATCHBACK
  BAKKIE
  COUPE
  CONVERTIBLE
  WAGON
  TRUCK
}

enum ServiceCategory {
  EXPRESS
  PREMIUM
  DELUXE
  EXECUTIVE
}

enum BookingStatus {
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  CANCELLED
}

enum PaymentMethodType {
  VISA
  MASTERCARD
  AMERICAN_EXPRESS
  DISCOVERY
  CASH
  EFT
  STRIPE_CARD
  STRIPE_WALLET
}

enum MembershipPlan {
  BASIC
  PREMIUM
  ELITE
}

enum NotificationType {
  BOOKING
  PROMOTION
  REMINDER
  SYSTEM
  PAYMENT
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}

// NextAuth.js Models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Admin Models
model AdminUser {
  id            String    @id @default(cuid())
  username      String    @unique
  email         String    @unique
  password      String    // Hashed password
  name          String
  role          String    @default("ADMIN") // ADMIN, SUPER_ADMIN
  isActive      Boolean   @default(true)
  lastLoginAt   DateTime?
  failedLogins  Int       @default(0)
  lockedUntil   DateTime?
  twoFactorSecret String? // For 2FA
  twoFactorEnabled Boolean @default(false)
  allowedIPs    String[]  // IP whitelist
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  auditLogs     AdminAuditLog[]

  @@index([email])
  @@index([username])
  @@index([isActive])
}

model AdminAuditLog {
  id            String    @id @default(cuid())
  adminUserId   String
  action        String    // "CREATE_SERVICE", "UPDATE_PRICE", "DELETE_USER", etc.
  resourceType  String    // "SERVICE", "USER", "BOOKING", etc.
  resourceId    String?   // ID of affected resource
  oldValue      String?   // Previous value (JSON)
  newValue      String?   // New value (JSON)
  ipAddress     String
  userAgent     String?
  createdAt     DateTime  @default(now())

  adminUser     AdminUser @relation(fields: [adminUserId], references: [id])

  @@index([adminUserId])
  @@index([action])
  @@index([createdAt])
}

// Core Application Models
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  password      String?
  name          String?
  firstName     String?
  lastName      String?
  phone         String?
  dateOfBirth   DateTime?
  gender        Gender?
  address       String?
  city          String?
  province      String?
  language      String?   @default("en")
  profileImage  String?
  isAdmin       Boolean   @default(false)
  loyaltyPoints Int       @default(0)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts         Account[]
  sessions         Session[]
  vehicles         Vehicle[]
  bookings         Booking[]
  paymentMethods   PaymentMethod[]
  membership       Membership?
  reviews          Review[]
  notifications    Notification[]
  stripeCustomer   StripeCustomer?
  waitlists        Waitlist[]

  // Indexes for performance
  @@index([email])
  @@index([isAdmin])
  @@index([loyaltyPoints])
  @@index([createdAt])
}

model StripeCustomer {
  id                String   @id @default(cuid())
  userId            String   @unique
  stripeCustomerId  String   @unique  // Stripe Customer ID
  email             String?  // Email used in Stripe
  name              String?  // Name used in Stripe
  phone             String?  // Phone used in Stripe
  defaultPaymentMethodId String? // Default Stripe Payment Method ID
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([stripeCustomerId])
  @@index([email])
}

model Vehicle {
  id           String      @id @default(cuid())
  userId       String
  make         String
  model        String
  year         Int
  color        String
  licensePlate String
  vehicleType  VehicleType
  isPrimary    Boolean     @default(false)
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  bookings Booking[]

  // Indexes for performance
  @@index([userId])
  @@index([licensePlate])
  @@index([isPrimary])
  @@unique([licensePlate]) // License plates should be unique
}

model Service {
  id          String          @id @default(cuid())
  name        String          @unique
  description String
  shortDesc   String
  price       Int             // Price in cents (R80 = 8000)
  duration    Int             // Duration in minutes
  category    ServiceCategory
  features    String[]        // Array of features
  isActive    Boolean         @default(true)
  rating      Float           @default(0)
  reviewCount Int             @default(0)
  imageUrl    String?         // Service image URL
  isPromoted  Boolean         @default(false) // For promotions
  promotionPrice Int?         // Promotional price in cents
  promotionEndDate DateTime?  // When promotion ends
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Relations
  bookings Booking[]
  addOns   ServiceAddOn[]
  reviews  Review[]

  // Indexes for performance
  @@index([category])
  @@index([isActive])
  @@index([price])
  @@index([rating])
}

model ServiceAddOn {
  id          String   @id @default(cuid())
  serviceId   String
  name        String
  description String
  price       Int      // Price in cents
  isActive    Boolean  @default(true)
  imageUrl    String?  // Add-on image URL
  isPromoted  Boolean  @default(false)
  promotionPrice Int?  // Promotional price in cents
  createdAt   DateTime @default(now())

  service       Service        @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  bookingAddOns BookingAddOn[]

  // Indexes for performance
  @@index([serviceId])
  @@index([isActive])
  @@index([price])
}

model Booking {
  id                 String        @id @default(cuid())
  userId             String
  serviceId          String
  vehicleId          String
  bookingDate        DateTime
  timeSlot           String
  status             BookingStatus @default(CONFIRMED)
  totalAmount        Int           // Total in cents
  baseAmount         Int           // Service base price
  addOnAmount        Int           @default(0)
  notes              String?
  cancellationReason String?
  cancelledAt        DateTime?
  completedAt        DateTime?
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt

  user      User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  service   Service           @relation(fields: [serviceId], references: [id])
  vehicle   Vehicle           @relation(fields: [vehicleId], references: [id])
  addOns    BookingAddOn[]
  payment   Payment?
  reminders BookingReminder[]

  // Indexes for performance
  @@index([userId])
  @@index([serviceId])
  @@index([vehicleId])
  @@index([bookingDate])
  @@index([status])
  @@index([createdAt])
}

model BookingAddOn {
  id        String @id @default(cuid())
  bookingId String
  addOnId   String
  quantity  Int    @default(1)
  price     Int    // Price at time of booking

  booking Booking      @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  addOn   ServiceAddOn @relation(fields: [addOnId], references: [id])

  // Indexes for performance
  @@index([bookingId])
  @@index([addOnId])
}

model PaymentMethod {
  id                String            @id @default(cuid())
  userId            String
  type              PaymentMethodType
  lastFour          String?           // Optional for non-card payments
  expiryMonth       Int?              // Optional for non-card payments
  expiryYear        Int?              // Optional for non-card payments
  cardholderName    String?           // Optional for non-card payments
  isDefault         Boolean           @default(false)
  isActive          Boolean           @default(true)

  // Stripe-specific fields
  stripePaymentMethodId String?       // Stripe Payment Method ID
  stripeBrand           String?       // Card brand from Stripe (visa, mastercard, etc.)
  stripeFingerprint     String?       // Stripe card fingerprint for duplicate detection

  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments Payment[]

  // Indexes for performance
  @@index([userId])
  @@index([isDefault])
  @@index([isActive])
  @@index([stripePaymentMethodId])
}

model Payment {
  id              String         @id @default(cuid())
  bookingId       String?        @unique
  paymentMethodId String?
  amount          Int            // Amount in cents
  status          PaymentStatus  @default(PENDING)
  transactionId   String?        // Generic transaction ID
  paymentDate     DateTime?

  // Payment method type for display
  paymentMethodType String?      // 'card', 'cash', 'eft', 'payfast', etc.

  // Stripe-specific fields
  stripePaymentIntentId String?  // Stripe Payment Intent ID
  stripeChargeId        String?  // Stripe Charge ID
  stripeCustomerId      String?  // Stripe Customer ID
  stripeFee             Int?     // Stripe processing fee in cents
  stripeReceiptUrl      String?  // Stripe receipt URL

  // Additional payment metadata
  currency        String         @default("ZAR") // South African Rand
  description     String?        // Payment description
  failureReason   String?        // Reason for payment failure
  refundReason    String?        // Reason for refund
  refundedAmount  Int?           // Amount refunded in cents

  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  booking       Booking?       @relation(fields: [bookingId], references: [id])
  paymentMethod PaymentMethod? @relation(fields: [paymentMethodId], references: [id])

  // Indexes for performance
  @@index([status])
  @@index([paymentDate])
  @@index([transactionId])
  @@index([stripePaymentIntentId])
  @@index([stripeChargeId])
  @@index([stripeCustomerId])
  @@index([createdAt])
}

model Membership {
  id        String         @id @default(cuid())
  userId    String         @unique
  plan      MembershipPlan
  price     Int            // Monthly price in cents
  startDate DateTime
  endDate   DateTime?
  isActive  Boolean        @default(true)
  autoRenew Boolean        @default(true)
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([plan])
  @@index([isActive])
  @@index([startDate])
  @@index([endDate])
}

model Review {
  id        String   @id @default(cuid())
  userId    String
  serviceId String
  rating    Int      // 1-5 stars
  comment   String?
  isVisible Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  service Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([userId])
  @@index([serviceId])
  @@index([rating])
  @@index([isVisible])
  @@index([createdAt])

  // Ensure one review per user per service
  @@unique([userId, serviceId])
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  title     String
  message   String
  type      NotificationType
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([userId])
  @@index([type])
  @@index([isRead])
  @@index([createdAt])
}

model BookingReminder {
  id           String    @id @default(cuid())
  bookingId    String
  reminderType String    // "24_hour", "2_hour", "30_minute"
  emailSent    Boolean   @default(false)
  smsSent      Boolean   @default(false)
  sentAt       DateTime?
  message      String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  booking Booking @relation(fields: [bookingId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([bookingId])
  @@index([reminderType])
  @@index([sentAt])
  @@unique([bookingId, reminderType]) // One reminder per type per booking
}

model Waitlist {
  id               String    @id @default(cuid())
  userId           String
  customerName     String
  email            String
  phone            String
  preferredDate    DateTime
  preferredTime    String?
  serviceId        String
  vehicleInfo      String?
  alternativeTimes String[]  // JSON array of alternative time preferences
  status           String    @default("ACTIVE") // ACTIVE, NOTIFIED, CONVERTED, CANCELLED
  priority         Int       @default(1) // Higher number = higher priority
  notificationsSent Int      @default(0)
  cancelledAt      DateTime?
  convertedAt      DateTime? // When waitlist converted to booking
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([userId])
  @@index([preferredDate])
  @@index([serviceId])
  @@index([status])
  @@index([priority])
  @@index([createdAt])
}
